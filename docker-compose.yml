version: '3.8'

services:
  ollama:
    image: ollama/ollama:latest
    container_name: openevolve-ollama
    ports:
      - "11434:11434"
    volumes:
      - ollama_data:/root/.ollama
    environment:
      - OLLAMA_HOST=0.0.0.0:11434
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:11434/api/tags"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 30s
    restart: unless-stopped
    networks:
      - openevolve-network

  model-puller:
    image: curlimages/curl:latest
    container_name: openevolve-model-puller
    depends_on:
      ollama:
        condition: service_healthy
    command: >
      sh -c "
        echo 'Pulling gemma2:2b model...' &&
        curl -X POST http://ollama:11434/api/pull -d '{\"name\":\"gemma2:2b\"}' &&
        echo 'Model pull completed!'
      "
    networks:
      - openevolve-network

  openevolve:
    build: .
    container_name: openevolve-main
    depends_on:
      model-puller:
        condition: service_completed_successfully
    volumes:
      - .:/app
    working_dir: /app
    environment:
      - OPENAI_API_KEY=ollama
    command: >
      python openevolve-run.py
        projects/compression/initial_program.py
        projects/compression/evaluator.py
        --config projects/compression/config.yaml
        --iterations 1000
    networks:
      - openevolve-network

networks:
  openevolve-network:
    driver: bridge

volumes:
  ollama_data:
    driver: local
