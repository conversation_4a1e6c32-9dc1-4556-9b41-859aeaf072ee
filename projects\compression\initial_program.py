# EVOLVE-BLOCK-START
"""Compression algorithm discovery for OpenEvolve"""
import struct
from typing import Tuple, List, Dict, Any


def compress_data(data: bytes) -> bytes:
    """
    A simple run-length encoding (RLE) compression algorithm.
    This basic implementation can be evolved into more sophisticated compression schemes.
    
    Args:
        data: Input bytes to compress
        
    Returns:
        Compressed bytes
    """
    if not data:
        return b''
    
    compressed = []
    current_byte = data[0]
    count = 1
    
    for i in range(1, len(data)):
        if data[i] == current_byte and count < 255:
            count += 1
        else:
            # Store count and byte
            compressed.extend([count, current_byte])
            current_byte = data[i]
            count = 1
    
    # Don't forget the last run
    compressed.extend([count, current_byte])
    
    return bytes(compressed)


def decompress_data(compressed_data: bytes) -> bytes:
    """
    Decompress data that was compressed with the compress_data function.
    
    Args:
        compressed_data: Compressed bytes
        
    Returns:
        Decompressed bytes
    """
    if not compressed_data or len(compressed_data) % 2 != 0:
        return b''
    
    decompressed = []
    
    for i in range(0, len(compressed_data), 2):
        count = compressed_data[i]
        byte_value = compressed_data[i + 1]
        decompressed.extend([byte_value] * count)
    
    return bytes(decompressed)


def get_compression_stats(original_data: bytes, compressed_data: bytes) -> Dict[str, float]:
    """
    Calculate compression statistics.
    
    Args:
        original_data: Original uncompressed data
        compressed_data: Compressed data
        
    Returns:
        Dictionary with compression statistics
    """
    if not original_data:
        return {"compression_ratio": 0.0, "space_savings": 0.0}
    
    original_size = len(original_data)
    compressed_size = len(compressed_data)
    
    compression_ratio = compressed_size / original_size if original_size > 0 else float('inf')
    space_savings = (original_size - compressed_size) / original_size if original_size > 0 else 0.0
    
    return {
        "compression_ratio": compression_ratio,
        "space_savings": space_savings,
        "original_size": original_size,
        "compressed_size": compressed_size
    }


# EVOLVE-BLOCK-END


def run_compression_test(test_data: bytes) -> Dict[str, Any]:
    """
    Run a complete compression test on the given data.
    This function remains fixed and provides the interface for evaluation.
    
    Args:
        test_data: Data to test compression on
        
    Returns:
        Dictionary with test results
    """
    try:
        # Compress the data
        compressed = compress_data(test_data)
        
        # Decompress to verify correctness
        decompressed = decompress_data(compressed)
        
        # Check if decompression is correct
        is_correct = decompressed == test_data
        
        # Get compression statistics
        stats = get_compression_stats(test_data, compressed)
        
        return {
            "success": True,
            "correct_decompression": is_correct,
            "compression_ratio": stats["compression_ratio"],
            "space_savings": stats["space_savings"],
            "original_size": stats["original_size"],
            "compressed_size": stats["compressed_size"]
        }
        
    except Exception as e:
        return {
            "success": False,
            "error": str(e),
            "compression_ratio": float('inf'),
            "space_savings": 0.0,
            "correct_decompression": False
        }


if __name__ == "__main__":
    # Test with some sample data
    test_cases = [
        b"aaaaaabbbbccccdddd",  # Repetitive data (good for RLE)
        b"abcdefghijklmnop",    # Non-repetitive data
        b"hello world hello world",  # Text with repetition
        b"\x00\x00\x00\x00\xFF\xFF\xFF\xFF",  # Binary data
    ]
    
    for i, test_data in enumerate(test_cases):
        print(f"\nTest case {i+1}: {test_data}")
        result = run_compression_test(test_data)
        print(f"Result: {result}")
