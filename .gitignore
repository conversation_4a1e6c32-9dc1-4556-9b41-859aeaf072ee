# Python
__pycache__/
*.py[cod]
*$py.class
*.so
.Python
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
wheels/
*.egg-info/
.installed.cfg
*.egg
MANIFEST

# Virtual environments
venv/
env/
ENV/

# IDE
.idea/
.vscode/
*.swp
*.swo

# Output files
examples/*/output/
openevolve_output*/
*.log

# Test cache
.pytest_cache/
.coverage
htmlcov/

# Misc
.DS_Store
.venv

# For SR
secrets.yaml
problems