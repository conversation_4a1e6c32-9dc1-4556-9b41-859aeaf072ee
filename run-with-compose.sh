#!/bin/bash

echo "Starting OpenEvolve with Docker Compose..."
echo "This will:"
echo "1. Start Ollama service"
echo "2. Pull the gemma2:2b model"
echo "3. Start OpenEvolve compression algorithm discovery"
echo ""

# Build and start services
docker-compose up --build

echo ""
echo "To stop the services, run: docker-compose down"
echo "To view logs: docker-compose logs -f"
echo "To restart just OpenEvolve: docker-compose restart openevolve"
