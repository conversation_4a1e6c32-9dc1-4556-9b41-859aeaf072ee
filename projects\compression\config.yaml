# Configuration for compression algorithm discovery
max_iterations: 200
checkpoint_interval: 20
log_level: "INFO"

# LLM configuration
llm:
  primary_model: "llama3.1-8b"
  primary_model_weight: 0.8
  secondary_model: "llama-4-scout-17b-16e-instruct"
  secondary_model_weight: 0.2
  api_base: "https://api.cerebras.ai/v1"
  temperature: 0.7
  top_p: 0.95
  max_tokens: 4096

# Prompt configuration
prompt:
  system_message: "You are an expert programmer specializing in data compression algorithms. Your task is to improve a compression algorithm to achieve better compression ratios while maintaining correctness and reasonable performance. Focus on the compress_data and decompress_data functions. Consider techniques like run-length encoding, dictionary-based compression, Hu<PERSON>man coding, LZ77/LZ78, or novel approaches. Ensure that decompressed data exactly matches the original input. Optimize for compression effectiveness on various data types including repetitive text, binary data, and mixed content."
  num_top_programs: 3
  use_template_stochasticity: true

# Database configuration
database:
  population_size: 60
  archive_size: 25
  num_islands: 4
  elite_selection_ratio: 0.2
  exploitation_ratio: 0.7

# Evaluator configuration
evaluator:
  timeout: 120
  cascade_evaluation: true
  cascade_thresholds: [0.3, 0.6]
  parallel_evaluations: 4
  use_llm_feedback: false

# Evolution settings
diff_based_evolution: true
allow_full_rewrites: false
