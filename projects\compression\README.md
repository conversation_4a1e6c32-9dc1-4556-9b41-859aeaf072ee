# Compression Algorithm Discovery with OpenEvolve

This project uses OpenEvolve to discover and evolve compression algorithms, starting from a simple run-length encoding (RLE) implementation and evolving towards more sophisticated compression techniques.

## Overview

The system evaluates compression algorithms on various data types:
- Highly repetitive data (good for RLE)
- Text data with patterns
- Semi-random data with some patterns
- Random data (challenging to compress)
- Binary data with patterns
- Mixed data types

## Files

- `initial_program.py` - Starting compression algorithm (simple RLE)
- `evaluator.py` - Comprehensive evaluation system that tests compression ratio, correctness, and speed
- `config.yaml` - Configuration for the evolution process
- `README.md` - This file

## Running with Docker Compose (Recommended)

The Docker Compose setup ensures Ollama and OpenEvolve can communicate reliably:

### Prerequisites
- Docker and Docker Compose installed
- At least 4GB of available RAM for the Ollama model

### Quick Start

**On Linux/Mac:**
```bash
chmod +x run-with-compose.sh
./run-with-compose.sh
```

**On Windows (PowerShell):**
```powershell
.\run-with-compose.ps1
```

**Manual Docker Compose:**
```bash
docker-compose up --build
```

### What happens:
1. **Ollama Service**: Starts Ollama server in a container
2. **Model Pulling**: Downloads the `gemma2:2b` model (about 1.6GB)
3. **OpenEvolve**: Starts the evolution process for compression algorithms

### Monitoring Progress

View logs in real-time:
```bash
docker-compose logs -f openevolve
```

View Ollama logs:
```bash
docker-compose logs -f ollama
```

### Stopping the Services

```bash
docker-compose down
```

To also remove the downloaded model data:
```bash
docker-compose down -v
```

## Running Locally (Alternative)

If you prefer to run with your local Ollama instance:

1. Make sure Ollama is running locally
2. Update `config.yaml` to use `http://host.docker.internal:11434/v1`
3. Run: `./command.sh`

## Configuration

Key configuration options in `config.yaml`:

- `max_iterations`: Number of evolution iterations (default: 200)
- `checkpoint_interval`: Save progress every N iterations (default: 20)
- `llm.primary_model`: Primary model for code generation
- `llm.secondary_model`: Secondary model for diversity
- `database.population_size`: Number of programs to maintain (default: 60)

## Expected Evolution

The algorithm should evolve from simple RLE to more sophisticated techniques:

1. **Initial**: Basic run-length encoding
2. **Early Evolution**: Improved RLE with better handling of edge cases
3. **Mid Evolution**: Dictionary-based compression, pattern recognition
4. **Advanced**: Huffman coding, LZ77/LZ78-style algorithms, hybrid approaches

## Evaluation Metrics

The evaluator measures:
- **Compression Ratio**: Lower is better (compressed_size / original_size)
- **Space Savings**: Higher is better (1 - compression_ratio)
- **Correctness**: Decompressed data must match original exactly
- **Speed**: Faster compression/decompression is better
- **Reliability**: Percentage of test cases that succeed

## Troubleshooting

### Model Download Issues
If the model download fails, you can manually pull it:
```bash
docker-compose exec ollama ollama pull gemma2:2b
```

### Memory Issues
The `gemma2:2b` model requires about 2GB of RAM. If you have limited memory, you can:
1. Use a smaller model like `gemma2:1b` (update config.yaml)
2. Reduce `max_iterations` and `population_size`

### Connection Issues
If OpenEvolve can't connect to Ollama:
1. Check that both services are in the same Docker network
2. Verify Ollama health check passes: `docker-compose ps`
3. Check Ollama logs: `docker-compose logs ollama`

## Results

Results are saved in `projects/compression/openevolve_output/`:
- `checkpoints/` - Periodic saves of the evolution state
- `best_program.py` - The best compression algorithm found
- `logs/` - Detailed evolution logs

Each checkpoint contains the best program found up to that point, allowing you to track the evolution of the compression algorithm over time.
