#!/usr/bin/env pwsh

Write-Host "Starting OpenEvolve with Docker Compose..." -ForegroundColor Green
Write-Host "This will:" -ForegroundColor Yellow
Write-Host "1. Start Ollama service" -ForegroundColor Yellow
Write-Host "2. Pull the gemma2:2b model" -ForegroundColor Yellow
Write-Host "3. Start OpenEvolve compression algorithm discovery" -ForegroundColor Yellow
Write-Host ""

# Build and start services
docker-compose up --build

Write-Host ""
Write-Host "To stop the services, run: docker-compose down" -ForegroundColor Cyan
Write-Host "To view logs: docker-compose logs -f" -ForegroundColor Cyan
Write-Host "To restart just OpenEvolve: docker-compose restart openevolve" -ForegroundColor Cyan
