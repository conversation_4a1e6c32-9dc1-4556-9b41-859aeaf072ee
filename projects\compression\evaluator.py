"""
Evaluator for compression algorithm discovery
"""

import importlib.util
import numpy as np
import time
import traceback
import random
from typing import List


def run_with_timeout(func, args=(), kwargs={}, timeout_seconds=10):
    """
    Run a function with a simple timeout mechanism.
    For now, we'll just run the function directly to avoid platform issues.

    Args:
        func: Function to run
        args: Arguments to pass to the function
        kwargs: Keyword arguments to pass to the function
        timeout_seconds: Timeout in seconds (currently ignored)

    Returns:
        Result of the function
    """
    # Simple execution without timeout for cross-platform compatibility
    try:
        return func(*args, **kwargs)
    except Exception as e:
        raise e


def generate_test_data() -> List[bytes]:
    """Generate various types of test data for compression evaluation"""
    test_data = []

    # 1. Highly repetitive data (should compress very well)
    test_data.append(b'A' * 1000)
    test_data.append(b'ABAB' * 250)
    test_data.append(b'\x00' * 500 + b'\xFF' * 500)

    # 2. Text data with patterns
    text1 = "The quick brown fox jumps over the lazy dog. " * 20
    test_data.append(text1.encode('utf-8'))

    text2 = "compression algorithm discovery test data " * 25
    test_data.append(text2.encode('utf-8'))

    # 3. Semi-random data with some patterns
    pattern_data = b''
    for _ in range(100):
        pattern_data += random.choice([b'ABC', b'DEF', b'GHI', b'JKL']) * random.randint(1, 5)
    test_data.append(pattern_data)

    # 4. Random data (should not compress well)
    random.seed(42)  # For reproducibility
    random_data = bytes([random.randint(0, 255) for _ in range(1000)])
    test_data.append(random_data)

    # 5. Binary data with patterns
    binary_pattern = b'\x00\x01\x02\x03' * 250
    test_data.append(binary_pattern)

    # 6. Mixed data
    mixed_data = b'Header: ' + b'X' * 100 + b' Middle: ' + b'Y' * 200 + b' End: ' + b'Z' * 50
    test_data.append(mixed_data)

    # 7. Empty and small data
    test_data.append(b'')
    test_data.append(b'A')
    test_data.append(b'AB')

    return test_data


def safe_float(value, default=0.0):
    """Convert a value to float safely"""
    try:
        result = float(value)
        if np.isnan(result) or np.isinf(result):
            return default
        return result
    except (TypeError, ValueError):
        return default


def evaluate(program_path):
    """
    Evaluate the compression program by testing it on various data types
    and measuring compression ratio, correctness, and performance.

    Args:
        program_path: Path to the program file

    Returns:
        Dictionary of metrics
    """
    try:
        # Load the program
        spec = importlib.util.spec_from_file_location("program", program_path)
        program = importlib.util.module_from_spec(spec)
        spec.loader.exec_module(program)

        # Check if the required function exists
        if not hasattr(program, "run_compression_test"):
            return {
                "compression_score": 0.0,
                "correctness_score": 0.0,
                "speed_score": 0.0,
                "overall_score": 0.0,
                "error": "Missing run_compression_test function",
            }

        # Generate test data
        test_datasets = generate_test_data()

        # Track results
        compression_ratios = []
        correctness_results = []
        execution_times = []
        space_savings = []
        success_count = 0

        for i, test_data in enumerate(test_datasets):
            try:
                start_time = time.time()

                # Run compression test with timeout
                result = run_with_timeout(
                    program.run_compression_test,
                    args=(test_data,),
                    timeout_seconds=5
                )

                end_time = time.time()
                execution_time = end_time - start_time

                # Validate result format
                if not isinstance(result, dict):
                    print(f"Test {i}: Invalid result format, expected dict but got {type(result)}")
                    continue

                # Extract metrics
                success = result.get("success", False)
                correct = result.get("correct_decompression", False)
                compression_ratio = safe_float(result.get("compression_ratio", float('inf')))
                space_saving = safe_float(result.get("space_savings", 0.0))

                if success and correct:
                    compression_ratios.append(compression_ratio)
                    correctness_results.append(1.0)
                    space_savings.append(space_saving)
                    success_count += 1
                else:
                    compression_ratios.append(float('inf'))
                    correctness_results.append(0.0)
                    space_savings.append(0.0)

                execution_times.append(execution_time)

            except TimeoutError:
                print(f"Test {i}: Timeout")
                compression_ratios.append(float('inf'))
                correctness_results.append(0.0)
                space_savings.append(0.0)
                execution_times.append(5.0)  # Max time
                continue
            except Exception as e:
                print(f"Test {i}: Error - {str(e)}")
                compression_ratios.append(float('inf'))
                correctness_results.append(0.0)
                space_savings.append(0.0)
                execution_times.append(1.0)
                continue

        # Calculate aggregate metrics
        if success_count == 0:
            return {
                "compression_score": 0.0,
                "correctness_score": 0.0,
                "speed_score": 0.0,
                "overall_score": 0.0,
                "error": "All tests failed",
            }

        # Correctness score (percentage of tests that passed)
        correctness_score = float(np.mean(correctness_results))

        # Compression score (lower compression ratio is better, but avoid division by zero)
        valid_ratios = [r for r in compression_ratios if r != float('inf') and r > 0]
        if valid_ratios:
            avg_compression_ratio = float(np.mean(valid_ratios))
            # Convert to score where higher is better (good compression = low ratio)
            compression_score = float(1.0 / (1.0 + avg_compression_ratio))
        else:
            compression_score = 0.0

        # Space savings score (higher is better)
        avg_space_savings = float(np.mean(space_savings))
        space_savings_score = float(max(0.0, avg_space_savings))  # Clamp to [0, 1]

        # Speed score (faster is better)
        avg_time = float(np.mean(execution_times))
        speed_score = float(1.0 / (1.0 + avg_time))

        # Reliability score
        reliability_score = float(success_count / len(test_datasets))

        # Combined compression effectiveness (balance ratio and space savings)
        compression_effectiveness = (compression_score + space_savings_score) / 2.0

        # Overall score prioritizes correctness and compression effectiveness
        overall_score = float(
            0.4 * correctness_score +
            0.35 * compression_effectiveness +
            0.15 * reliability_score +
            0.1 * speed_score
        )

        return {
            "compression_score": compression_score,
            "space_savings_score": space_savings_score,
            "compression_effectiveness": compression_effectiveness,
            "correctness_score": correctness_score,
            "speed_score": speed_score,
            "reliability_score": reliability_score,
            "overall_score": overall_score,
            "success_rate": reliability_score,
            "avg_compression_ratio": float(np.mean(valid_ratios)) if valid_ratios else float('inf'),
            "avg_space_savings": avg_space_savings,
        }

    except Exception as e:
        print(f"Evaluation failed completely: {str(e)}")
        print(traceback.format_exc())
        return {
            "compression_score": 0.0,
            "correctness_score": 0.0,
            "speed_score": 0.0,
            "overall_score": 0.0,
            "error": str(e),
        }


# Stage-based evaluation for cascade evaluation
def evaluate_stage1(program_path):
    """First stage evaluation with basic functionality check"""
    try:
        # Load the program
        spec = importlib.util.spec_from_file_location("program", program_path)
        program = importlib.util.module_from_spec(spec)
        spec.loader.exec_module(program)

        # Check if required function exists
        if not hasattr(program, "run_compression_test"):
            return {"runs_successfully": 0.0, "error": "Missing run_compression_test function"}

        # Quick test with simple data
        test_data = b"AAAA"
        try:
            result = run_with_timeout(
                program.run_compression_test,
                args=(test_data,),
                timeout_seconds=2
            )

            if isinstance(result, dict) and result.get("success", False):
                correctness = 1.0 if result.get("correct_decompression", False) else 0.5
                return {
                    "runs_successfully": 1.0,
                    "basic_correctness": correctness,
                    "overall_score": correctness,
                }
            else:
                return {"runs_successfully": 0.5, "basic_correctness": 0.0, "overall_score": 0.0}

        except Exception as e:
            print(f"Stage 1 evaluation failed: {e}")
            return {"runs_successfully": 0.0, "error": str(e)}

    except Exception as e:
        print(f"Stage 1 evaluation failed: {e}")
        return {"runs_successfully": 0.0, "error": str(e)}


def evaluate_stage2(program_path):
    """Second stage evaluation with full testing"""
    return evaluate(program_path)


if __name__ == "__main__":
    # Test the evaluator
    import sys
    if len(sys.argv) > 1:
        program_path = sys.argv[1]
    else:
        program_path = "initial_program.py"

    print(f"Testing evaluator with program: {program_path}")
    try:
        result = evaluate(program_path)
        print(f"Evaluation result: {result}")
    except Exception as e:
        print(f"Error during evaluation: {e}")
        import traceback
        traceback.print_exc()
